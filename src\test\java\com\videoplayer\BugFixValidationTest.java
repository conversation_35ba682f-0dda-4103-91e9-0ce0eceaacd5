package com.videoplayer;

import com.videoplayer.config.OssConfig;
import com.videoplayer.service.OssService;
import com.videoplayer.service.VideoService;
import com.videoplayer.entity.Video;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Bug修复验证测试
 * 验证修复的bug不会再次出现
 */
@SpringBootTest
@ActiveProfiles("test")
public class BugFixValidationTest {

    @Autowired(required = false)
    private OssService ossService;

    @Autowired
    private VideoService videoService;

    @Autowired
    private OssConfig ossConfig;

    /**
     * 测试OSS配置验证功能
     */
    @Test
    public void testOssConfigValidation() {
        // OSS配置应该能够正常初始化，即使配置不完整也不应该抛出异常
        assertNotNull(ossConfig);
        
        // 验证配置验证方法不会抛出异常
        assertDoesNotThrow(() -> {
            ossConfig.validateConfig();
        });
    }

    /**
     * 测试VideoService空值处理
     */
    @Test
    public void testVideoServiceNullHandling() {
        // 测试空ID处理
        assertTrue(videoService.getVideoById(null).isEmpty());
        assertTrue(videoService.getVideoById("").isEmpty());
        assertTrue(videoService.getVideoById("   ").isEmpty());
        
        // 测试空视频对象处理
        assertThrows(IllegalArgumentException.class, () -> {
            videoService.saveVideo(null);
        });
        
        // 测试更新视频时的空值处理
        assertThrows(IllegalArgumentException.class, () -> {
            videoService.updateVideo(null, new Video());
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            videoService.updateVideo("test-id", null);
        });
    }

    /**
     * 测试置顶功能的空值处理
     */
    @Test
    public void testPinStatusNullHandling() {
        // 测试空ID处理
        assertFalse(videoService.toggleVideoPinStatus(null));
        assertFalse(videoService.toggleVideoPinStatus(""));
        assertFalse(videoService.toggleVideoPinStatus("   "));
        
        assertFalse(videoService.setVideoPinStatus(null, true));
        assertFalse(videoService.setVideoPinStatus("", false));
        assertFalse(videoService.setVideoPinStatus("   ", true));
    }

    /**
     * 测试OSS服务在客户端不可用时的处理
     */
    @Test
    public void testOssServiceNullClientHandling() {
        if (ossService != null) {
            // 如果OSS服务可用，测试删除文件的空值处理
            assertDoesNotThrow(() -> {
                ossService.deleteFile(null);
                ossService.deleteFile("");
                ossService.deleteFile("   ");
            });
        }
    }

    /**
     * 测试Video实体的基本功能
     */
    @Test
    public void testVideoEntityBasicFunctionality() {
        Video video = new Video();
        
        // 验证ID自动生成
        assertNotNull(video.getId());
        assertEquals(18, video.getId().length());
        
        // 验证默认值
        assertTrue(video.getIsActive());
        assertFalse(video.getIsPinned());
        assertNotNull(video.getCreatedTime());
        assertNotNull(video.getUpdatedTime());
        
        // 测试构造函数
        Video video2 = new Video("测试标题", "http://example.com/video.mp4");
        assertEquals("测试标题", video2.getTitle());
        assertEquals("http://example.com/video.mp4", video2.getVideoUrl());
    }

    /**
     * 测试视频总数统计功能
     */
    @Test
    public void testVideoCountFunctionality() {
        // 获取视频总数不应该抛出异常
        assertDoesNotThrow(() -> {
            long count = videoService.getTotalVideoCount();
            assertTrue(count >= 0);
        });
    }
}
