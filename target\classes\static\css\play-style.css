/**
 * 视频播放页面专用样式 - 响应式设计 - 强制防夜间模式
 * Video Play Page Styles - Responsive Design - Force Light Mode
 */

/* 播放页面防夜间模式设置 - 包含内联样式 */
:root {
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

html, body {
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

/* 从HTML内联样式移动过来的防夜间模式设置 */
/* Body样式 - 从内联样式移动 */
body {
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
}

/* 导航栏样式 - 从内联样式移动 */
.navbar.navbar-dark.bg-primary.sticky-top {
    background-color: #0d6efd !important;
    color-scheme: light only !important;
}

/* 优化内联播放的视频播放器样式 */
#video-player {
    width: 100%;
    height: 100%; /* 填充整个容器高度 */
    max-width: 100%;
    background-color: #000;
    border-radius: 8px;
    object-fit: contain; /* 保持视频比例，完整显示 */
    display: block;
    /* 确保内联播放 */
    -webkit-playsinline: true;
    -webkit-appearance: none;
    /* 移动端优化 */
    touch-action: manipulation;
}

/* 播放器容器 - 响应式 */
.video-player-container {
    position: relative;
    background: #000;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(170, 169, 169, 0.1);
    margin-bottom: 1.5rem;
    width: 100%;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    border: 1px solid #e9ecef;
}

/* 视频包装器 - 自适应高度 */
.video-wrapper {
    position: relative;
    width: 100%;
    height: 400px; /* 固定合适的高度 */
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.video-wrapper #video-player {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 400px;
    border-radius: 8px;
}

/* 视频信息区域 */
.video-info {
    background: #fff;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin: 1px 0px !important;
}

/* 覆盖Bootstrap的mb-4类，确保margin设置生效 */
.video-info.bg-white.rounded-3.shadow-sm.p-4.mb-4 {
    margin: 1px 0px !important;
    height: 100px !important;
}

/* 视频标题 */
.video-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
    text-align: center;
}

/* 特定选择器：确保video-title h3 mb-3的内容居中 */
.video-title.h3.mb-3 {
    text-align: center !important;
}


/* 全屏按钮 */
.fullscreen-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    z-index: 1000;
    transition: background 0.3s ease;
}

.fullscreen-btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

/* 响应式设计 - 移动端内联播放优化 */
@media (max-width: 768px) {
    .video-player-container {
        margin: 0 auto 1rem;
        border-radius: 8px;
        max-width: 100%;
        /* 移动端内联播放优化 */
        position: relative;
        overflow: hidden;
    }

    .video-wrapper {
        height: 400px; /* 移动端适合的高度 */
        /* 确保视频容器不会触发全屏 */
        position: relative;
        overflow: hidden;
    }

    #video-player {
        /* 移动端内联播放关键样式 */
        position: absolute;
        top: 0;
        left: 0;
        width: 100% !important;
        height: 100% !important;
        object-fit: contain;
        /* 防止iOS Safari自动全屏 */
        -webkit-playsinline: true !important;
        /* 防止Android浏览器自动全屏 */
        x5-video-player-type: h5;
        x5-video-player-fullscreen: true;
    }

    .video-info {
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 8px;
    }

    .video-title {
        font-size: 1.25rem;
        text-align: center;
    }

    .video-stats {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .video-actions {
        width: 100%;
        text-align: center;
    }
}



/* 错误状态 */
.video-error {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 8px;
    color: #6c757d;
}

.video-error i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #dc3545;
}


/* 平板设备优化 */
@media (max-width: 991.98px) {
    .video-player-container {
        max-width: 500px;
    }

    .video-wrapper {
        height: 400px; /* 平板设备适合的高度 */
    }
}

@media (max-width: 576px) {
    /* 小屏幕设备内联播放优化 */
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .video-player-container {
        margin: 0 0 1rem 0;
        border-radius: 8px;
        max-width: 100%;
        /* 小屏幕内联播放优化 */
        position: relative;
        overflow: hidden;
    }

    .video-wrapper {
        height: 400px; /* 小屏幕设备适合的高度 */
        position: relative;
        overflow: hidden;
    }

    #video-player {
        /* 小屏幕内联播放关键设置 */
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        object-fit: contain !important;
        /* 强制内联播放 */
        -webkit-playsinline: true !important;
        playsinline: true !important;
        /* 微信浏览器内联播放 */
        x5-video-player-type: h5 !important;
        x5-video-player-fullscreen: true !important;
        x5-video-orientation: portraint !important;
    }

    .video-info {
        margin: 0.5rem 0;
        padding: 0.75rem;
    }

    .video-title {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }

    .video-description {
        font-size: 0.9rem;
    }
}

/* 主容器样式调整 */
.container.my-4 {
    margin-top: 10px !important;
}



/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .video-info {
        background: #2d3748;
        color: #e2e8f0;
    }

    .video-title {
        color: #f7fafc;
    }


}



/* 视频描述样式 */
.video-description {
    border-top: 1px solid #e9ecef;
    padding-top: 1rem;
    margin-top: 1rem;
}

.video-description p {
    line-height: 1.6;
    color: #6c757d;
}

/* 视频包装器高度已在上方定义，此处删除重复设置 */

/* 确保视频在所有设备上都能内联播放 */
video {
    -webkit-playsinline: true !important;
    -moz-playsinline: true !important;
    -ms-playsinline: true !important;
    playsinline: true !important;
}

/* 移动端浏览器内联播放优化 */
video::-webkit-media-controls-start-playback-button {
    display: none !important;
}

/* 防止视频在移动端自动全屏 */
@media screen and (max-width: 768px) {
    video {
        object-position: center;
        object-fit: contain;
        width: 100% !important;
        height: 100% !important;
        max-width: 100% !important;
        max-height: 100% !important;
    }
}

/* ========== 播放页面最终防夜间模式保护 ========== */
@media screen {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}

@media screen and (max-device-width: 768px) {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}