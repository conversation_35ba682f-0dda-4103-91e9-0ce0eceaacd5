package com.videoplayer.config;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 阿里云OSS配置类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
@ConfigurationProperties(prefix = "aliyun.oss")
public class OssConfig {

    private static final Logger logger = LoggerFactory.getLogger(OssConfig.class);

    private String endpoint;
    private String accessKeyId;
    private String accessKeySecret;
    private String bucketName;
    private String videoDir;
    private String thumbnailDir;
    private String baseUrl;

    /**
     * 配置验证
     */
    @PostConstruct
    public void validateConfig() {
        if (!StringUtils.hasText(endpoint)) {
            logger.warn("OSS endpoint 未配置，OSS功能将不可用");
        }
        if (!StringUtils.hasText(accessKeyId)) {
            logger.warn("OSS accessKeyId 未配置，OSS功能将不可用");
        }
        if (!StringUtils.hasText(accessKeySecret)) {
            logger.warn("OSS accessKeySecret 未配置，OSS功能将不可用");
        }
        if (!StringUtils.hasText(bucketName)) {
            logger.warn("OSS bucketName 未配置，OSS功能将不可用");
        }

        if (StringUtils.hasText(endpoint) && StringUtils.hasText(accessKeyId) &&
            StringUtils.hasText(accessKeySecret) && StringUtils.hasText(bucketName)) {
            logger.info("OSS配置验证通过");
        }
    }

    /**
     * 创建OSS客户端Bean
     * 添加配置验证，避免空指针异常
     */
    @Bean
    public OSS ossClient() {
        if (!StringUtils.hasText(endpoint) || !StringUtils.hasText(accessKeyId) || !StringUtils.hasText(accessKeySecret)) {
            logger.warn("OSS配置不完整，返回null客户端");
            return null;
        }

        try {
            OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            logger.info("OSS客户端创建成功");
            return ossClient;
        } catch (Exception e) {
            logger.error("OSS客户端创建失败: {}", e.getMessage(), e);
            return null;
        }
    }

    // Getter和Setter方法
    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getVideoDir() {
        return videoDir;
    }

    public void setVideoDir(String videoDir) {
        this.videoDir = videoDir;
    }

    public String getThumbnailDir() {
        return thumbnailDir;
    }

    public void setThumbnailDir(String thumbnailDir) {
        this.thumbnailDir = thumbnailDir;
    }

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }
}
