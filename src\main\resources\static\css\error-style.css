/**
 * 错误页面专用样式 - 强制防夜间模式
 * Error Page Styles - Force Light Mode
 */

/* 错误页面防夜间模式设置 - 包含内联样式 */
:root {
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

html, body {
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

/* 从HTML内联样式移动过来的防夜间模式设置 */
/* 这些样式确保页面始终保持浅色主题 */

/* Body样式 - 从内联样式移动 */
body {
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
}

/* 导航栏样式 - 从内联样式移动 */
.navbar.navbar-expand-lg.navbar-dark.bg-primary.sticky-top {
    background-color: #0d6efd !important;
    color-scheme: light only !important;
}

/* 错误容器样式 */
.error-container {
    text-align: center;
    padding: 3rem 2rem;
    max-width: 800px;
    margin: 0 auto;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 20px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.error-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(13, 110, 253, 0.05) 0%, transparent 70%);
    animation: pulse 4s ease-in-out infinite;
}

/* 错误图标样式 */
.error-icon {
    font-size: 6rem;
    color: #dc3545;
    margin-bottom: 2rem;
    position: relative;
    z-index: 1;
    animation: bounce 2s ease-in-out infinite;
}

.error-icon i {
    filter: drop-shadow(0 4px 8px rgba(220, 53, 69, 0.3));
}

/* 错误标题样式 */
.display-4 {
    position: relative;
    z-index: 1;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    background: linear-gradient(45deg, #dc3545, #fd7e14);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 警告框样式增强 */
.alert {
    border: none;
    border-radius: 12px;
    font-weight: 500;
    position: relative;
    z-index: 1;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c2c7 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border-left: 4px solid #ffc107;
}

/* 按钮样式增强 */
.btn {
    border-radius: 25px;
    font-weight: 600;
    padding: 0.75rem 2rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-primary {
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    border: none;
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0b5ed7, #5a0fc8);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.4);
}

.btn-outline-secondary {
    border: 2px solid #6c757d;
    color: #6c757d;
    background: transparent;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.3);
}

/* 解决方案列表样式 */
.list-unstyled {
    text-align: left;
    background: rgba(255, 255, 255, 0.8);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 1;
}

.list-unstyled li {
    padding: 0.5rem 0;
    font-weight: 500;
    color: #495057;
    transition: all 0.2s ease;
}

.list-unstyled li:hover {
    color: #0d6efd;
    transform: translateX(5px);
}

.list-unstyled h5 {
    color: #495057;
    font-weight: 700;
    margin-bottom: 1rem;
    text-align: center;
}

/* 页脚样式增强 */
.bg-dark {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%) !important;
}

footer {
    position: relative;
    overflow: hidden;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

footer a:hover {
    color: #0d6efd !important;
    transition: color 0.3s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .error-container {
        padding: 2rem 1rem;
        margin: 1rem;
        border-radius: 15px;
    }
    
    .error-icon {
        font-size: 4rem;
        margin-bottom: 1.5rem;
    }
    
    .display-4 {
        font-size: 2.5rem;
    }
    
    .btn {
        padding: 0.6rem 1.5rem;
        font-size: 0.9rem;
        margin: 0.25rem;
        display: block;
        width: 100%;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
    }
    
    .list-unstyled {
        padding: 1rem;
    }
}

@media (max-width: 576px) {
    .error-container {
        padding: 1.5rem 0.75rem;
        margin: 0.5rem;
    }
    
    .error-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
    }
    
    .display-4 {
        font-size: 2rem;
        margin-bottom: 1.5rem;
    }
    
    .alert {
        font-size: 0.9rem;
        padding: 0.75rem;
    }
    
    .btn {
        font-size: 0.85rem;
        padding: 0.5rem 1rem;
    }
}

/* 动画效果 */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.5;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.05);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* 加载动画 */
.loading-animation {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(13, 110, 253, 0.3);
    border-radius: 50%;
    border-top-color: #0d6efd;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* ========== 错误页面最终防夜间模式保护 ========== */
@media screen {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}

@media screen and (max-device-width: 768px) {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}
