/**
 * 视频播放页面专用JavaScript功能
 * <AUTHOR>
 * @version 1.0.0
 */

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializeVideoPlayer();
    initializeInlinePlaybackOptimization();
    initializeSearchToggle();
});

/**
 * 初始化视频播放器
 */
function initializeVideoPlayer() {
    const videoElement = document.getElementById('video-player');
    if (!videoElement) return;

    const videoUrl = videoElement.dataset.videoUrl;
    const videoId = videoElement.dataset.videoId;

    // 设置视频源
    if (videoUrl) {
        videoElement.src = videoUrl;
    }

    // 应用内联播放优化
    optimizeInlinePlayback(videoElement);

    // 生成缩略图
    generateThumbnail(videoElement);

    // 添加事件监听器
    addVideoEventListeners(videoElement, videoId);

    console.log('响应式HTML5视频播放器已准备就绪');
}

/**
 * 优化内联播放设置
 * @param {HTMLVideoElement} videoElement - 视频元素
 */
function optimizeInlinePlayback(videoElement) {
    // 确保内联播放属性设置正确
    videoElement.setAttribute('playsinline', 'true');
    videoElement.setAttribute('webkit-playsinline', 'true');

    // 移动端优化
    if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
        // iOS Safari 内联播放优化
        videoElement.setAttribute('webkit-playsinline', 'true');
        videoElement.setAttribute('playsinline', 'true');

        // Android 微信/QQ浏览器内联播放优化
        videoElement.setAttribute('x5-video-player-type', 'h5');
        videoElement.setAttribute('x5-video-player-fullscreen', 'true');
        videoElement.setAttribute('x5-video-orientation', 'portraint');

        // 禁用自动全屏
        videoElement.setAttribute('webkit-playsinline', 'true');
        videoElement.muted = false; // 确保不是静音状态
    }

    // 设置视频尺寸适应容器
    videoElement.style.width = '100%';
    videoElement.style.height = 'auto';
    videoElement.style.objectFit = 'contain';
}

/**
 * 生成视频首帧缩略图
 * @param {HTMLVideoElement} videoElement - 视频元素
 */
function generateThumbnail(videoElement) {
    if (!videoElement.poster) {
        videoElement.addEventListener('loadeddata', function() {
            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // 设置画布尺寸
                canvas.width = videoElement.videoWidth;
                canvas.height = videoElement.videoHeight;

                // 绘制视频首帧
                ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

                // 转换为base64图片
                const thumbnailDataUrl = canvas.toDataURL('image/jpeg', 0.8);
                videoElement.poster = thumbnailDataUrl;

                console.log('视频首帧缩略图生成成功');
            } catch (error) {
                console.warn('无法生成视频缩略图:', error);
            }
        });
    }
}

/**
 * 添加视频事件监听器
 * @param {HTMLVideoElement} videoElement - 视频元素
 * @param {string} videoId - 视频ID
 */
function addVideoEventListeners(videoElement, videoId) {
    // 内联播放优化事件监听
    videoElement.addEventListener('loadstart', function() {
        console.log('开始加载视频');
        optimizeInlinePlayback(videoElement);
    });

    // 视频元数据加载完成
    videoElement.addEventListener('loadedmetadata', function() {
        console.log('视频元数据加载完成');
        optimizeInlinePlayback(videoElement);
    });

    // 视频数据加载完成
    videoElement.addEventListener('loadeddata', function() {
        console.log('视频数据加载完成');
    });

    // 视频可以播放
    videoElement.addEventListener('canplay', function() {
        console.log('视频可以播放');
    });

    // 播放开始时确保内联播放
    videoElement.addEventListener('play', function() {
        console.log('视频开始播放，ID:', videoId);
        // 防止移动端自动全屏
        if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
            videoElement.setAttribute('playsinline', 'true');
            videoElement.setAttribute('webkit-playsinline', 'true');
        }
    });

    // 视频暂停事件
    videoElement.addEventListener('pause', function() {
        console.log('视频已暂停，ID:', videoId);
    });

    // 视频结束事件
    videoElement.addEventListener('ended', function() {
        console.log('视频播放结束，ID:', videoId);
    });

    // 错误处理
    videoElement.addEventListener('error', function(e) {
        console.error('视频播放出错:', e);
        handleVideoError(videoElement);
    });

    // 等待事件
    videoElement.addEventListener('waiting', function() {
        console.log('视频缓冲中');
    });
}

/**
 * 处理视频播放错误
 * @param {HTMLVideoElement} videoElement - 视频元素
 */
function handleVideoError(videoElement) {
    let errorMessage = '视频加载失败，请检查网络连接。';

    if (videoElement.error) {
        console.error('错误代码:', videoElement.error.code);
        console.error('错误信息:', videoElement.error.message);

        switch (videoElement.error.code) {
            case 1:
                errorMessage = '视频加载被中止。';
                break;
            case 2:
                errorMessage = '网络错误，无法加载视频。';
                break;
            case 3:
                errorMessage = '视频解码失败或格式不支持。';
                break;
            case 4:
                errorMessage = '视频不存在或无法访问。';
                break;
        }
    }

    // 显示错误信息
    const errorDiv = document.createElement('div');
    errorDiv.className = 'alert alert-danger mt-3';
    errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${errorMessage}`;
    videoElement.parentNode.appendChild(errorDiv);
}

/**
 * 初始化内联播放优化
 */
function initializeInlinePlaybackOptimization() {
    const videoElement = document.getElementById('video-player');
    if (!videoElement) return;

    // 确保内联播放属性设置
    function ensureInlinePlayback() {
        videoElement.setAttribute('playsinline', 'true');
        videoElement.setAttribute('webkit-playsinline', 'true');
        if (/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)) {
            videoElement.setAttribute('x5-video-player-type', 'h5');
            videoElement.setAttribute('x5-video-player-fullscreen', 'true');
            videoElement.setAttribute('x5-video-orientation', 'portraint');
        }
    }

    // 添加额外的事件监听器
    videoElement.addEventListener('loadeddata', function() {
        console.log('视频加载完成');
        ensureInlinePlayback();
    });

    videoElement.addEventListener('play', function() {
        console.log('视频开始播放');
        ensureInlinePlayback();
    });

    videoElement.addEventListener('canplay', function() {
        console.log('视频可以播放');
        ensureInlinePlayback();
    });

    // 处理全屏变化事件
    document.addEventListener('fullscreenchange', function() {
        if (!document.fullscreenElement) {
            setTimeout(function() {
                optimizeInlinePlayback(videoElement);
            }, 100);
        }
    });

    document.addEventListener('webkitfullscreenchange', function() {
        if (!document.webkitFullscreenElement) {
            setTimeout(function() {
                optimizeInlinePlayback(videoElement);
            }, 100);
        }
    });

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            setTimeout(function() {
                optimizeInlinePlayback(videoElement);
            }, 100);
        }
    });

    // 初始化时确保内联播放设置
    ensureInlinePlayback();

    console.log('内联播放优化的视频播放器初始化完成');
}

/**
 * 初始化搜索切换功能
 */
function initializeSearchToggle() {
    const searchToggle = document.querySelector('.search-toggle');
    if (searchToggle) {
        // 移除内联onclick，添加事件监听器
        searchToggle.removeAttribute('onclick');
        searchToggle.addEventListener('click', toggleSearch);
    }
}

/**
 * 切换搜索框显示/隐藏 - 从HTML内联事件移动过来
 */
function toggleSearch() {
    const searchForm = document.querySelector('.search-form');
    const searchInput = document.querySelector('.search-input-mobile');

    if (searchForm && searchInput) {
        const isHidden = searchForm.style.display === 'none' || !searchForm.style.display;

        if (isHidden) {
            searchForm.style.display = 'flex';
            searchInput.focus();
        } else {
            searchForm.style.display = 'none';
        }
    }
}

// 导出到全局作用域
window.VideoPlayerPage = {
    optimizeInlinePlayback,
    generateThumbnail,
    handleVideoError,
    toggleSearch,
    initializeSearchToggle
};

// 导出toggleSearch函数供HTML调用
window.toggleSearch = toggleSearch;
