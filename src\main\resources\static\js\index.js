/**
 * 首页专用JavaScript功能
 * <AUTHOR>
 * @version 1.0.0
 */

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializeIndexPage();
});

/**
 * 初始化首页
 */
function initializeIndexPage() {
    // 初始化搜索功能
    initializeSearch();

    // 初始化缩略图加载
    initializeThumbnails();

    // 初始化页面动画 - 已禁用渐显效果
    // initializeAnimations();

    console.log('首页初始化完成');
}

/**
 * 初始化搜索功能
 */
function initializeSearch() {
    const searchForm = document.querySelector('.search-form');
    const searchInput = document.querySelector('.search-input-mobile');

    if (searchForm && searchInput) {
        // 添加搜索表单提交验证，阻止空搜索
        searchForm.addEventListener('submit', function(e) {
            const keyword = searchInput.value.trim();
            if (!keyword) {
                e.preventDefault();
                // 空搜索时不执行任何操作，静默阻止
            }
        });

        // 添加键盘事件支持
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // ESC键收起搜索框
                if (this.classList.contains('search-input-expanded')) {
                    this.classList.remove('search-input-expanded');
                    const searchSubmit = document.querySelector('.search-submit');
                    const searchToggle = document.querySelector('.search-toggle');
                    if (searchSubmit) searchSubmit.style.display = 'none';
                    if (searchToggle) searchToggle.style.display = 'inline-block';
                    this.blur(); // 失去焦点
                }
            }
        });

        // 添加输入框失焦事件，如果没有内容则收起搜索框
        searchInput.addEventListener('blur', function() {
            if (!this.value.trim() && this.classList.contains('search-input-expanded')) {
                setTimeout(() => {
                    if (!this.value.trim()) {
                        this.classList.remove('search-input-expanded');
                        const searchSubmit = document.querySelector('.search-submit');
                        const searchToggle = document.querySelector('.search-toggle');
                        if (searchSubmit) searchSubmit.style.display = 'none';
                        if (searchToggle) searchToggle.style.display = 'inline-block';
                    }
                }, 100); // 最小延迟，避免点击提交按钮时立即收起
            }
        });
    }
}



/**
 * 初始化缩略图加载
 */
function initializeThumbnails() {
    const thumbnails = document.querySelectorAll('.thumbnail-optimized');

    thumbnails.forEach(img => {
        // 移除内联onload和onerror事件，使用事件监听器
        img.removeAttribute('onload');
        img.removeAttribute('onerror');

        // 添加加载成功事件
        img.addEventListener('load', function() {
            this.classList.add('loaded');
            this.setAttribute('data-loaded', 'true');
        });

        // 添加加载失败事件
        img.addEventListener('error', function() {
            const fallbackSrc = this.getAttribute('data-fallback-src');
            if (fallbackSrc && this.src !== fallbackSrc) {
                this.src = fallbackSrc;
            } else {
                this.classList.add('error');
                this.alt = '图片加载失败';
            }
        });

        // 如果图片已经加载完成，立即添加loaded类
        if (img.complete && img.naturalHeight !== 0) {
            img.classList.add('loaded');
            img.setAttribute('data-loaded', 'true');
        }
    });
}

/**
 * 初始化页面动画 - 已禁用渐显效果
 */
function initializeAnimations() {
    // 渐显动画已禁用，确保所有元素直接显示
    const videoCards = document.querySelectorAll('.video-card');
    const heroSection = document.querySelector('.hero-section');

    // 确保视频卡片直接显示，无动画
    videoCards.forEach((card) => {
        card.style.opacity = '1';
        card.style.transform = 'translateY(0)';
        card.style.transition = 'none';
    });

    // 确保欢迎横幅直接显示，无动画
    if (heroSection) {
        heroSection.style.opacity = '1';
        heroSection.style.transform = 'translateY(0)';
        heroSection.style.transition = 'none';
    }

    console.log('页面动画已禁用，所有内容直接显示');
}

/**
 * 联系信息位置调整函数
 * @param {string} top - 顶部边距
 * @param {string} bottom - 底部边距  
 * @param {string} height - 高度
 */
function adjustContact(top, bottom, height) {
    const root = document.documentElement;
    if (top !== undefined) root.style.setProperty('--contact-margin-top', top);
    if (bottom !== undefined) root.style.setProperty('--contact-margin-bottom', bottom);
    if (height !== undefined) root.style.setProperty('--contact-height', height);
}

// 导出到全局作用域
window.IndexPage = {
    adjustContact,
    initializeSearch,
    initializeThumbnails,
    initializeAnimations
};
