<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="10a0d57b-7f98-48b2-bfcb-b98408b26c48" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="30W4YPMUGJVKpyYdYPStwMoVGuI" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.video-player [clean].executor": "Run",
    "Maven.video-player [package].executor": "Run",
    "Maven.video-player [validate].executor": "Run",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "Spring Boot.VideoPlayerApplication.executor": "Run",
    "kotlin-language-version-configured": "true",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="RunManager">
    <configuration name="VideoPlayerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="video-player" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.videoplayer.VideoPlayerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="10a0d57b-7f98-48b2-bfcb-b98408b26c48" name="Changes" comment="" />
      <created>1753730044868</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753730044868</updated>
      <workItem from="1753730046599" duration="416000" />
      <workItem from="1753742899193" duration="88000" />
      <workItem from="1753743613202" duration="92000" />
      <workItem from="1753744252118" duration="398000" />
      <workItem from="1753744699759" duration="5556000" />
      <workItem from="1753752710349" duration="9999000" />
      <workItem from="1753807967131" duration="118000" />
      <workItem from="1753808303464" duration="47000" />
      <workItem from="1753808366878" duration="631000" />
      <workItem from="1753838972463" duration="3324000" />
      <workItem from="1753850002796" duration="4897000" />
      <workItem from="1753893292561" duration="19090000" />
      <workItem from="1753985409347" duration="1509000" />
      <workItem from="1754004512868" duration="3227000" />
      <workItem from="1754070156219" duration="25000" />
      <workItem from="1754085085478" duration="4951000" />
      <workItem from="1754132843704" duration="5893000" />
      <workItem from="1754150328023" duration="2011000" />
      <workItem from="1754154009110" duration="11343000" />
      <workItem from="1754172054286" duration="2432000" />
      <workItem from="1754174993312" duration="13609000" />
      <workItem from="1754195265727" duration="2963000" />
      <workItem from="1754202879945" duration="5216000" />
      <workItem from="1754209913469" duration="9479000" />
      <workItem from="1754246701668" duration="905000" />
      <workItem from="1754264135460" duration="11686000" />
      <workItem from="1754283032969" duration="6922000" />
      <workItem from="1754328214125" duration="1162000" />
      <workItem from="1754845576726" duration="38052000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>