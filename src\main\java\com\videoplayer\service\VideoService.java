package com.videoplayer.service;

import com.videoplayer.entity.Video;
import com.videoplayer.repository.VideoRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 视频服务类
 */
@Service
@Transactional
public class VideoService {

    @Autowired
    private VideoRepository videoRepository;

    @Autowired(required = false)
    private OssService ossService;

    /**
     * 获取所有视频（分页）
     */
    public List<Video> getAllVideos(int page, int size) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<Video> videoPage = videoRepository.findByIsActiveTrue(pageable);
        List<Video> videos = videoPage.getContent();

        // 将置顶视频排在前面
        return sortWithPinnedVideo(videos);
    }

    /**
     * 搜索视频（分页，返回列表）
     */
    public List<Video> searchVideosList(String keyword, int page, int size) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(page, size, sort);
        List<Video> videos = videoRepository.searchVideos(keyword, pageable).getContent();

        // 将置顶视频排在前面
        return sortWithPinnedVideo(videos);
    }

    /**
     * 获取所有视频（包括禁用的，用于管理页面）
     */
    public List<Video> getAllVideosForAdmin() {
        List<Video> videos = videoRepository.findAll(Sort.by(Sort.Direction.DESC, "createdTime"));
        // 将置顶视频排在前面（管理页面也需要显示置顶状态）
        return sortWithPinnedVideoForAdmin(videos);
    }

    /**
     * 分页获取所有启用的视频
     */
    public Page<Video> getAllActiveVideos(int page, int size, String sortBy, String sortDir) {
        Sort sort = sortDir.equalsIgnoreCase("desc") ?
                   Sort.by(sortBy).descending() :
                   Sort.by(sortBy).ascending();
        Pageable pageable = PageRequest.of(page, size, sort);
        return videoRepository.findByIsActiveTrue(pageable);
    }

    /**
     * 根据ID获取视频
     */
    public Optional<Video> getVideoById(String id) {
        return videoRepository.findByIdAndIsActiveTrue(id);
    }

    /**
     * 保存视频
     */
    public Video saveVideo(Video video) {
        return videoRepository.save(video);
    }

    /**
     * 更新视频信息
     */
    public Video updateVideo(String id, Video videoDetails) {
        Optional<Video> optionalVideo = videoRepository.findById(id);
        if (optionalVideo.isPresent()) {
            Video video = optionalVideo.get();

            // 只更新非null的字段
            if (videoDetails.getTitle() != null) {
                video.setTitle(videoDetails.getTitle());
            }
            if (videoDetails.getDescription() != null) {
                video.setDescription(videoDetails.getDescription());
            }
            if (videoDetails.getVideoUrl() != null) {
                video.setVideoUrl(videoDetails.getVideoUrl());
            }
            if (videoDetails.getThumbnailUrl() != null) {
                video.setThumbnailUrl(videoDetails.getThumbnailUrl());
            }
            if (videoDetails.getFileSize() != null) {
                video.setFileSize(videoDetails.getFileSize());
            }
            if (videoDetails.getVideoFormat() != null) {
                video.setVideoFormat(videoDetails.getVideoFormat());
            }
            if (videoDetails.getResolution() != null) {
                video.setResolution(videoDetails.getResolution());
            }

            // 更新isActive字段
            if (videoDetails.getIsActive() != null) {
                video.setIsActive(videoDetails.getIsActive());
            }

            // 更新isPinned字段
            if (videoDetails.getIsPinned() != null) {
                video.setIsPinned(videoDetails.getIsPinned());
            }

            Video savedVideo = videoRepository.save(video);
            return savedVideo;
        }
        return null;
    }

    /**
     * 使用原生SQL强制永久删除视频记录
     */
    @Transactional
    public boolean forceDeleteVideoById(String id) {
        // 先检查视频是否存在且已禁用
        Optional<Video> optionalVideo = videoRepository.findById(id);
        if (optionalVideo.isPresent()) {
            Video video = optionalVideo.get();

            if (video.getIsActive()) {
                throw new IllegalStateException("只能删除已禁用的视频。请先禁用视频再进行删除操作。");
            }

            // 删除OSS文件
            if (ossService != null) {
                if (video.getVideoUrl() != null) {
                    ossService.deleteFile(video.getVideoUrl());
                }
                if (video.getThumbnailUrl() != null) {
                    ossService.deleteFile(video.getThumbnailUrl());
                }
            }

            // 使用原生SQL强制删除
            int deletedRows = videoRepository.deleteVideoById(id);

            if (deletedRows > 0) {
                return true;
            } else {
                throw new RuntimeException("删除失败：没有记录被删除");
            }
        }
        return false;
    }

    /**
     * 根据标题搜索视频
     */
    public List<Video> searchVideosByTitle(String title) {
        List<Video> videos = videoRepository.findByTitleContainingIgnoreCase(title);

        // 将置顶视频排在前面
        return sortWithPinnedVideo(videos);
    }

    /**
     * 分页搜索视频
     */
    public Page<Video> searchVideos(String keyword, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("createdTime").descending());
        return videoRepository.searchVideos(keyword, pageable);
    }

    /**
     * 获取最新视频（按创建时间排序）
     */
    public List<Video> getPopularVideos(int limit) {
        Sort sort = Sort.by(Sort.Direction.DESC, "createdTime");
        Pageable pageable = PageRequest.of(0, limit, sort);
        List<Video> videos = videoRepository.findByIsActiveTrue(pageable).getContent();

        // 将置顶视频排在前面
        return sortWithPinnedVideo(videos);
    }

    /**
     * 将置顶视频排在前面
     */
    private List<Video> sortWithPinnedVideo(List<Video> videos) {
        return videos.stream()
                .sorted((v1, v2) -> {
                    // 置顶视频排在最前面
                    boolean v1Pinned = v1.getIsPinned() != null && v1.getIsPinned();
                    boolean v2Pinned = v2.getIsPinned() != null && v2.getIsPinned();

                    if (v1Pinned && !v2Pinned) {
                        return -1;
                    } else if (!v1Pinned && v2Pinned) {
                        return 1;
                    } else {
                        // 同样置顶状态的视频按创建时间倒序排列
                        return v2.getCreatedTime().compareTo(v1.getCreatedTime());
                    }
                })
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 管理页面专用排序：置顶视频排在前面（包括禁用的视频）
     */
    private List<Video> sortWithPinnedVideoForAdmin(List<Video> videos) {
        return videos.stream()
                .sorted((v1, v2) -> {
                    // 置顶视频排在最前面
                    boolean v1Pinned = v1.getIsPinned() != null && v1.getIsPinned();
                    boolean v2Pinned = v2.getIsPinned() != null && v2.getIsPinned();

                    if (v1Pinned && !v2Pinned) {
                        return -1;
                    } else if (!v1Pinned && v2Pinned) {
                        return 1;
                    } else {
                        // 同样置顶状态的视频按创建时间倒序排列
                        return v2.getCreatedTime().compareTo(v1.getCreatedTime());
                    }
                })
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 切换视频置顶状态
     */
    @Transactional
    public boolean toggleVideoPinStatus(String id) {
        Optional<Video> optionalVideo = videoRepository.findById(id);
        if (optionalVideo.isPresent()) {
            Video video = optionalVideo.get();
            boolean currentPinStatus = video.getIsPinned() != null && video.getIsPinned();
            boolean newPinStatus = !currentPinStatus;

            video.setIsPinned(newPinStatus);
            videoRepository.save(video);
            return true;
        }
        return false;
    }

    /**
     * 设置视频置顶状态
     */
    @Transactional
    public boolean setVideoPinStatus(String id, boolean isPinned) {
        Optional<Video> optionalVideo = videoRepository.findById(id);
        if (optionalVideo.isPresent()) {
            Video video = optionalVideo.get();
            video.setIsPinned(isPinned);
            videoRepository.save(video);
            return true;
        }
        return false;
    }

    /**
     * 获取视频总数
     */
    public long getTotalVideoCount() {
        return videoRepository.countByIsActiveTrue();
    }
}
