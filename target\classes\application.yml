server:
  port: 5000

spring:
  application:
    name: video-player

  # 数据库配置
  datasource:
    url: ************************************************************************************************************************************************************************************
#    username: video_player
#    password: jZzB8EJwnFHBNXBt
    username: root
    password: root

    driver-class-name: com.mysql.cj.jdbc.Driver

  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect

  # Thymeleaf配置
  thymeleaf:
    prefix: classpath:/templates/
    suffix: .html
    cache: false
    encoding: UTF-8
    mode: HTML

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
# 日志配置
logging:
  level:
    com.videoplayer: INFO
    org.springframework.web: WARN
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 阿里云OSS配置
aliyun:
  oss:
    endpoint: ${OSS_ENDPOINT:https://oss-cn-guangzhou.aliyuncs.com}
    access-key-id: ${OSS_ACCESS_KEY_ID:your-access-key-id}
    access-key-secret: ${OSS_ACCESS_KEY_SECRET:your-access-key-secret}
    bucket-name: ${OSS_BUCKET_NAME:your-bucket-name}
    video-dir: ${OSS_VIDEO_DIR:video-player/videos}
    thumbnail-dir: ${OSS_THUMBNAIL_DIR:video-player/thumbnails}
    base-url: ${OSS_BASE_URL:https://your-bucket-name.oss-cn-guangzhou.aliyuncs.com}
