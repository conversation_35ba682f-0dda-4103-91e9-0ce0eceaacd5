/**
 * 管理界面专用样式 - 强制防夜间模式
 * Admin Page Styles - Force Light Mode
 */

/* 管理页面防夜间模式设置 */
:root {
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

html, body {
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}





/* 视频缩略图 */
.video-thumbnail {
    width: 80px;
    height: 45px;
    object-fit: cover;
    border-radius: 4px;
}

/* 缩略图优化样式 */
.thumbnail-optimized {
    transition: opacity 0.3s ease-in-out;
    opacity: 0.7;
    background-color: #f8f9fa;
}

.thumbnail-optimized.loaded {
    opacity: 1;
    background-image: none;
}

/* 视频标题 */
.video-title {
    font-weight: 600;
    color: #2c3e50;
}

/* 视频描述 */
.video-description {
    color: #6c757d;
    font-size: 0.875rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 状态徽章 */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* 操作按钮 */
.action-btn {
    padding: 0.25rem 0.5rem;
    margin: 0 1px;
    white-space: nowrap; /* 防止按钮内容换行 */
    flex-shrink: 0; /* 防止按钮被压缩 */
}

/* 搜索按钮边框颜色 */
.btn-primary {
    border-color: #6c757d !important;
    margin-left: 6px;
}

.btn.btn-primary{
    margin-left: 6px!important;
} 
.btn.btn-outline-secondary{
    margin-left: 2px!important;
}

/* 删除input-group下按钮的所有点击效果 */
.input-group .btn:hover,
.input-group .btn:focus,
.input-group .btn:active,
.input-group .btn:focus-visible {
    transform: none !important;
    box-shadow: none !important;
    outline: none !important;
    background-color: inherit !important;
    border-color: inherit !important;
    color: inherit !important;
}

/* input-group按钮圆角美化 */
.input-group .btn {
    border-radius: 6px !important;
}

.input-group .form-control {
    border-radius: 6px !important;
}

/* 确保input-group整体的圆角效果 */
.input-group {
    border-radius: 6px;
}

/* 删除form-control的点击效果 */
.form-control:focus,
.form-control:active,
.form-control:focus-visible {
    box-shadow: none !important;
    outline: none !important;
    border-color: inherit !important;
    background-color: inherit !important;
}

/* 搜索区域 */
.search-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

/* 表格容器 */
.table-container {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* 修复sticky定位的层级问题 */
.navbar.sticky-top {
    z-index: 1030 !important;
}

.table-responsive .table thead.sticky-top {
    z-index: 1020 !important;
}

.table-responsive {
    position: relative;
    z-index: 1010 !important;
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch !important;
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}



/* 禁用的视频行 */
.video-row.disabled {
    opacity: 0.6;
    background-color: #f8f9fa;
}

/* ========== 输入框文本操作功能确保 ========== */

/* 确保所有输入框和表单控件支持完整的文本操作功能 */
input,
textarea,
select,
.form-control,
.form-select,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
input[type="url"],
input[type="tel"],
input[type="number"] {
    user-select: text !important;
    -webkit-user-select: text !important;
    -moz-user-select: text !important;
    -ms-user-select: text !important;
    pointer-events: auto !important;
    cursor: text !important;
}

/* 确保输入框在获得焦点时支持文本选择 */
input:focus,
textarea:focus,
select:focus,
.form-control:focus,
.form-select:focus {
    user-select: text !important;
    -webkit-user-select: text !important;
    cursor: text !important;
    outline: none !important;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25) !important;
}

/* 修改复选框的勾选标记颜色为黑色 */
.form-check-input:checked {
    background-color: #ffffff !important;
    border-color: #000000 !important;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23000000' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e") !important;
}

.form-check-input:checked:focus {
    background-color: #ffffff !important;
    border-color: #000000 !important;
    box-shadow: 0 0 0 0.25rem rgba(0, 0, 0, 0.25) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-section {
        padding: 1rem;
    }

    .video-thumbnail {
        width: 60px;
        height: 34px;
    }

    .action-btn {
        padding: 0.2rem 0.4rem;
        margin: 0;
    }
    .d-flex.gap-01 {
        background-color: #198754 !important;
    }

    /* 手机端添加按钮样式 - 确保图标和文字为白色 */
    .btn-success .fas.fa-plus,
    .btn-success {
        color: white !important;
    }

    .btn-success:hover .fas.fa-plus,
    .btn-success:hover {
        color: white !important;
    }

    .btn-success:focus .fas.fa-plus,
    .btn-success:focus {
        color: white !important;
    }

    .btn-success:active .fas.fa-plus,
    .btn-success:active {
        color: white !important;
    }

    .btn-success:visited .fas.fa-plus,
    .btn-success:visited {
        color: white !important;
    }
}

/* 打印样式 */
@media print {
    .search-section,
    .action-btn,
    .loading-overlay {
        display: none !important;
    }
    
    .table-container {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* 可点击状态样式 */
.clickable-status {
    transition: all 0.2s ease;
    user-select: none;
}

.clickable-status:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.clickable-status:active {
    transform: scale(0.95);
}

.clickable-status.bg-success:hover {
    background-color: #198754 !important;
}

.clickable-status.bg-secondary:hover {
    background-color: #6c757d !important;
}

/* 覆盖任何可能阻止输入框文本选择的样式 */
.search-section input,
.search-section textarea,
.search-section select,
.search-section .form-control,
.search-section .form-select {
    user-select: text !important;
    -webkit-user-select: text !important;
    pointer-events: auto !important;
    cursor: text !important;
}

/* ID折叠样式 */
.id-container {
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.id-collapsed {
    max-width: 50px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

.id-expanded {
    max-width: none;
    word-break: break-all;
}

.id-toggle-icon {
    font-size: 0.8em;
    margin-left: 4px;
    transition: transform 0.3s ease;
}

.id-expanded .id-toggle-icon {
    transform: rotate(180deg);
}

.id-container:hover {
    background-color: #e9ecef !important;
    transform: scale(1.02);
}

.id-full-text {
    font-family: 'Courier New', monospace;
    font-size: 0.85em;
    letter-spacing: 0.5px;
}

/* 分页样式 */
/* 上一页和下一页按钮样式 - 点击时颜色不变 */
.pagination .page-item:first-child .page-link,
.pagination .page-item:last-child .page-link {
    color: #6c757d !important;
    background-color: #fff !important;
    border-color: #dee2e6 !important;
}

.pagination .page-item:first-child .page-link:hover,
.pagination .page-item:last-child .page-link:hover {
    color: #6c757d !important;
    background-color: #e9ecef !important;
    border-color: #dee2e6 !important;
}

.pagination .page-item:first-child .page-link:focus,
.pagination .page-item:last-child .page-link:focus {
    color: #6c757d !important;
    background-color: #fff !important;
    border-color: #dee2e6 !important;
    box-shadow: none !important;
}

/* 上一页和下一页按钮点击时的active状态 */
.pagination .page-item:first-child .page-link:active,
.pagination .page-item:last-child .page-link:active {
    color: #6c757d !important;
    background-color: #fff !important;
    border-color: #dee2e6 !important;
    box-shadow: none !important;
}

/* 上一页和下一页按钮的visited状态 */
.pagination .page-item:first-child .page-link:visited,
.pagination .page-item:last-child .page-link:visited {
    color: #6c757d !important;
    background-color: #fff !important;
    border-color: #dee2e6 !important;
}

/* 确保所有状态组合都被覆盖 */
.pagination .page-item:first-child .page-link:hover:focus,
.pagination .page-item:last-child .page-link:hover:focus,
.pagination .page-item:first-child .page-link:hover:active,
.pagination .page-item:last-child .page-link:hover:active {
    color: #6c757d !important;
    background-color: #e9ecef !important;
    border-color: #dee2e6 !important;
    box-shadow: none !important;
}

/* 禁用状态的上一页和下一页 */
.pagination .page-item.disabled:first-child .page-link,
.pagination .page-item.disabled:last-child .page-link {
    color: #6c757d !important;
    background-color: #fff !important;
    border-color: #dee2e6 !important;
}

/* 页码按钮改为和上一页下一页相同的颜色 */
.pagination .page-item:not(:first-child):not(:last-child).active .page-link {
    color: #6c757d !important;
    background-color: #e9ecef !important;
    border-color: #dee2e6 !important;
}

.pagination .page-item:not(:first-child):not(:last-child) .page-link {
    color: #6c757d !important;
    background-color: #fff !important;
    border-color: #dee2e6 !important;
}

.pagination .page-item:not(:first-child):not(:last-child) .page-link:hover {
    color: #6c757d !important;
    background-color: #e9ecef !important;
    border-color: #dee2e6 !important;
}

/* ========== 管理页面最终防夜间模式保护 ========== */
@media screen {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}

@media screen and (max-device-width: 768px) {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}