<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="light only">
    <meta name="theme-color" content="#ffffff">
    <meta name="msapplication-navbutton-color" content="#ffffff">
    <meta name="apple-mobile-web-app-status-bar-style" content="light-content">
    <meta name="supported-color-schemes" content="light">
    <!-- 强制防夜间模式 -->
    <meta name="prefers-color-scheme" content="light">
    <meta name="force-color-scheme" content="light">

    <title th:text="${pageTitle}">佳茵轻康 - 视频库</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- 自定义CSS -->
    <link href="/css/style.css" rel="stylesheet">
    <link href="/css/videos-style.css" rel="stylesheet">



</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-dark bg-primary sticky-top">
        <div class="container">
            <div class="d-flex w-100 align-items-center justify-content-between">
                <!-- 导航链接 -->
                <div class="d-flex align-items-center">
                    <ul class="navbar-nav d-flex flex-row mb-0">
                        <li class="nav-item">
                            <a class="nav-link px-2" href="/">
                                <i class="fas fa-home me-1"></i>首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active px-2" href="/videos">
                                <i class="fas fa-video me-1"></i>视频
                            </a>
                        </li>

                    </ul>
                </div>

                <!-- 搜索框 -->
                <div class="search-container" th:data-has-keyword="${keyword != null and !keyword.isEmpty()}">
                    <form class="d-flex search-form" action="/videos" method="get">
                        <input class="form-control me-2 search-input-mobile" type="search" name="keyword"
                               th:value="${keyword}" placeholder="搜索...">
                        <button type="submit" class="btn btn-outline-light btn-sm search-submit" style="display: none;">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                    <button type="button" class="btn btn-outline-light btn-sm search-toggle" onclick="toggleSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container my-4">




        <!-- 视频列表 -->
        <section class="mb-5">
            <!-- 无视频提示 -->
            <div th:if="${videos == null or videos.isEmpty()}" class="text-center py-5">
                <i class="fas fa-video fa-4x text-muted mb-4"></i>
                <h3 class="text-muted mb-4">
                    <span th:if="${keyword}">未找到相关视频</span>
                    <span th:unless="${keyword}">暂无视频</span>
                </h3>
                <p class="text-muted mb-4">
                    <span th:if="${keyword}">尝试使用其他关键词搜索，或者</span>
                    <span th:unless="${keyword}">还没有上传任何视频</span>
                </p>
            </div>

            <!-- 视频卡片网格 -->
            <div th:if="${videos != null and !videos.isEmpty()}" class="row g-4">
                <div class="col-lg-3 col-md-4 col-sm-6"
                     th:each="video : ${videos}"
                     th:style="${video.id == 1 ? 'order: -1;' : ''}">
                    <div class="video-card card h-100 shadow-sm"
                         th:class="${video.id == 1 ? 'video-card card h-100 shadow-sm pinned-video' : 'video-card card h-100 shadow-sm'}">
                        <!-- 置顶标识 -->
                        <div th:if="${video.id == 1}" class="pinned-badge">
                            <i class="fas fa-thumbtack"></i>
                            <span>置顶</span>
                        </div>

                        <div class="video-thumbnail position-relative">
                            <img th:src="${video.thumbnailUrl}"
                                 class="card-img-top thumbnail-optimized"
                                 th:alt="${video.title}"
                                 loading="lazy"
                                 decoding="async"
                                 th:data-fallback-src="${video.thumbnailUrl}"
                                 data-loaded="false">
                            <div class="play-overlay">
                                <a th:href="@{/play/{id}(id=${video.id})}" class="play-btn">
                                    <i class="fas fa-play fa-2x"></i>
                                </a>
                            </div>
                        </div>
                        <div class="card-body">
                            <h5 class="card-title" th:text="${video.title}">视频标题</h5>
<!--                            <p class="video-description" th:text="${video.description}">视频描述</p>-->
                            <div class="video-stats">
                                <div class="mb-1">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>
                                        <span th:text="${#temporals.format(video.createdTime, 'yyyy-MM-dd')}">2024-01-01</span>
                                    </small>
                                </div>
<!--                                <div>-->
<!--                                    <small class="text-muted">-->
<!--                                        <i class="fas fa-eye me-1"></i>-->
<!--                                        <span th:text="${video.viewCount}">0</span> 次播放-->
<!--                                    </small>-->
<!--                                </div>-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 分页导航 -->
        <div th:if="${videos != null and !videos.isEmpty()}" class="d-flex justify-content-center align-items-center">
            <nav>
                <ul class="pagination mb-0">
                    <li class="page-item" th:classappend="${currentPage == 0 ? 'disabled' : ''}">
                        <a th:if="${currentPage > 0}" class="page-link" th:href="@{/videos(page=${currentPage - 1}, size=${pageSize}, keyword=${keyword})}"
                           aria-label="上一页">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <span th:if="${currentPage == 0}" class="page-link" aria-label="上一页">
                            <i class="fas fa-chevron-left"></i>
                        </span>
                    </li>
                    <li class="page-item active">
                        <span class="page-link" th:text="${currentPage + 1}">1</span>
                    </li>
                    <li class="page-item" th:classappend="${!hasNextPage ? 'disabled' : ''}">
                        <a th:if="${hasNextPage == true}" class="page-link" th:href="@{/videos(page=${currentPage + 1}, size=${pageSize}, keyword=${keyword})}"
                           aria-label="下一页">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                        <span th:if="${hasNextPage == false or hasNextPage == null}" class="page-link" aria-label="下一页">
                            <i class="fas fa-chevron-right"></i>
                        </span>
                    </li>
                </ul>
            </nav>

            <!-- 总页数信息 - 无边框样式，绝对定位到最右边 -->
            <div class="pagination-total-info-no-border">
                 共 <span th:text="${totalPages}">0</span> 页
                <i class="fas fa-layer-group me-1"></i>
               
            </div>
        </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-dark text-light py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p class="videos-footer-info"><i class="fas fa-play-circle me-2"></i>视频库 <span class="text-light">
                            有 <span th:text="${videos.size()}">0</span> 个视频
                        </span></p>
                    <p class="mb-0">
                        <small>© 2025 佳茵轻康. <a href="/about" class="text-light text-decoration-none">点这里👉<span>联系我们</span></a></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/js/main.js"></script>
    <script src="/js/videos.js"></script>
</body>
</html>
