/**
 * 视频列表页面专用JavaScript功能
 * <AUTHOR>
 * @version 1.0.0
 */

// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initializeVideosPage();
});

/**
 * 初始化视频列表页面
 */
function initializeVideosPage() {
    initializeLazyLoading();
    initializeVideoCards();
    initializePagination();
    initializeSearchToggle();
    initializeSearchForm();
    initializeThumbnails();
    console.log('视频列表页面初始化完成');
}

/**
 * 初始化懒加载
 */
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    img.classList.add('loaded');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(function(img) {
            imageObserver.observe(img);
        });
    } else {
        // 降级处理
        images.forEach(function(img) {
            img.src = img.dataset.src;
            img.classList.add('loaded');
        });
    }
}

/**
 * 初始化视频卡片
 */
function initializeVideoCards() {
    const videoCards = document.querySelectorAll('.video-card');
    
    videoCards.forEach(function(card) {
        // 添加悬停效果
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'transform 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
        
        // 处理图片加载错误
        const img = card.querySelector('img');
        if (img) {
            img.addEventListener('error', function() {
                this.src = '/images/default-thumbnail.jpg';
                this.alt = '默认缩略图';
            });
        }
    });
}

/**
 * 初始化分页
 */
function initializePagination() {
    // 处理禁用的分页项
    const disabledPageItems = document.querySelectorAll('.pagination .page-item.disabled');
    disabledPageItems.forEach(function(item) {
        const links = item.querySelectorAll('a, span');
        links.forEach(function(link) {
            // 阻止点击事件
            link.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('禁用的分页按钮被点击，已阻止跳转');
                showAlert('已经是最后一页了', 'warning');
                return false;
            });

            // 移除href属性以确保不会跳转
            if (link.tagName === 'A') {
                link.removeAttribute('href');
                link.style.cursor = 'not-allowed';
                link.style.pointerEvents = 'none';
            }
        });
    });

    // 处理正常的分页链接
    const paginationLinks = document.querySelectorAll('.pagination .page-item:not(.disabled) a');
    paginationLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            // 移除加载状态，直接跳转
            // 不添加任何加载效果，保持原生的页面跳转体验
        });
    });

    // 调试信息
    console.log('分页初始化完成:', {
        disabledItems: disabledPageItems.length,
        activeLinks: paginationLinks.length
    });
}

/**
 * 初始化搜索表单
 */
function initializeSearchForm() {
    const searchForms = document.querySelectorAll('.search-form');
    searchForms.forEach(function(form) {
        const input = form.querySelector('input[name="keyword"]');

        // 表单提交事件 - 阻止空搜索
        form.addEventListener('submit', function(e) {
            if (input && input.value.trim() === '') {
                e.preventDefault();
                // 空搜索时不执行任何操作，静默阻止
            }
        });
    });
}

/**
 * 初始化搜索切换功能
 */
function initializeSearchToggle() {
    const searchContainer = document.querySelector('.search-container');
    const searchInput = document.querySelector('.search-input-mobile');
    const searchSubmit = document.querySelector('.search-submit');
    const searchToggle = document.querySelector('.search-toggle');

    // 检查是否有搜索关键词
    const hasKeyword = searchContainer && searchContainer.dataset.hasKeyword === 'true';

    if (hasKeyword && searchInput) {
        // 如果有搜索关键词，展开搜索框
        searchInput.classList.add('search-input-expanded');
        if (searchSubmit) searchSubmit.style.display = 'inline-block';
        if (searchToggle) searchToggle.style.display = 'none';
    }

    if (searchToggle) {
        // 移除内联onclick，添加事件监听器
        searchToggle.removeAttribute('onclick');
        searchToggle.addEventListener('click', toggleSearch);
    }

    // 添加键盘事件支持
    if (searchInput) {
        searchInput.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                // ESC键收起搜索框
                if (this.classList.contains('search-input-expanded')) {
                    this.classList.remove('search-input-expanded');
                    if (searchSubmit) searchSubmit.style.display = 'none';
                    if (searchToggle) searchToggle.style.display = 'inline-block';
                    this.blur(); // 失去焦点
                }
            }
        });

        // 添加输入框失焦事件，如果没有内容则收起搜索框
        searchInput.addEventListener('blur', function() {
            if (!this.value.trim() && this.classList.contains('search-input-expanded')) {
                setTimeout(() => {
                    if (!this.value.trim()) {
                        this.classList.remove('search-input-expanded');
                        if (searchSubmit) searchSubmit.style.display = 'none';
                        if (searchToggle) searchToggle.style.display = 'inline-block';
                    }
                }, 100); // 最小延迟，避免点击提交按钮时立即收起
            }
        });
    }
}

/**
 * 切换搜索框显示状态 - 使用统一的逻辑
 */
function toggleSearch() {
    // 使用全局的 toggleSearch 函数，如果存在的话
    if (window.toggleSearch && window.toggleSearch !== toggleSearch) {
        window.toggleSearch();
        return;
    }

    // 本地实现（与 main.js 中的逻辑保持一致）
    const searchInput = document.querySelector('.search-input-mobile');
    const searchSubmit = document.querySelector('.search-submit');
    const searchToggle = document.querySelector('.search-toggle');

    if (!searchInput) return;

    const isExpanded = searchInput.classList.contains('search-input-expanded');

    if (isExpanded) {
        // 如果搜索框已展开
        const keyword = searchInput.value.trim();
        if (keyword) {
            // 只有当输入框有内容时才提交搜索
            const form = searchInput.closest('form');
            if (form) {
                form.submit();
            }
        } else {
            // 如果输入框为空，收起搜索框
            searchInput.classList.remove('search-input-expanded');
            if (searchSubmit) searchSubmit.style.display = 'none';
            if (searchToggle) searchToggle.style.display = 'inline-block';
        }
    } else {
        // 展开搜索框
        searchInput.classList.add('search-input-expanded');
        if (searchSubmit) searchSubmit.style.display = 'inline-block';
        if (searchToggle) searchToggle.style.display = 'none';
        // 立即聚焦到输入框
        searchInput.focus();
    }
}

/**
 * 初始化缩略图加载
 */
function initializeThumbnails() {
    const thumbnails = document.querySelectorAll('.thumbnail-optimized');

    thumbnails.forEach(img => {
        // 移除内联onload和onerror事件，使用事件监听器
        img.removeAttribute('onload');
        img.removeAttribute('onerror');

        // 强制设置缩略图尺寸
        forceThumbnailSize(img);

        // 添加加载成功事件
        img.addEventListener('load', function() {
            this.classList.add('loaded');
            this.setAttribute('data-loaded', 'true');
            // 加载完成后再次确保尺寸正确
            forceThumbnailSize(this);
        });

        // 添加加载失败事件
        img.addEventListener('error', function() {
            const fallbackSrc = this.getAttribute('data-fallback-src');
            if (fallbackSrc && this.src !== fallbackSrc) {
                this.src = fallbackSrc;
            } else {
                this.classList.add('error');
                this.alt = '图片加载失败';
                // 错误时也要确保尺寸正确
                forceThumbnailSize(this);
            }
        });

        // 如果图片已经加载完成，立即添加loaded类
        if (img.complete && img.naturalHeight !== 0) {
            img.classList.add('loaded');
            img.setAttribute('data-loaded', 'true');
            forceThumbnailSize(img);
        }
    });

    // 监听窗口大小变化，重新调整缩略图尺寸
    window.addEventListener('resize', debounce(function() {
        thumbnails.forEach(forceThumbnailSize);
    }, 250));
}

/**
 * 强制设置缩略图尺寸
 * @param {HTMLImageElement} img - 图片元素
 */
function forceThumbnailSize(img) {
    if (!img) return;

    // 根据屏幕宽度设置不同的高度
    const screenWidth = window.innerWidth;
    let targetHeight;

    if (screenWidth <= 576) {
        targetHeight = 220; // 小屏幕 (160 + 60)
    } else if (screenWidth <= 768) {
        targetHeight = 240; // 中等屏幕 (180 + 60)
    } else {
        targetHeight = 260; // 大屏幕 (200 + 60)
    }

    // 强制设置样式
    img.style.width = '100%';
    img.style.height = targetHeight + 'px';
    img.style.objectFit = 'cover';
    img.style.display = 'block';
    img.style.minHeight = targetHeight + 'px';
    img.style.maxHeight = targetHeight + 'px';

    // 确保父容器也有正确的高度
    const container = img.closest('.video-thumbnail, .video-thumbnail-container');
    if (container) {
        container.style.height = targetHeight + 'px';
        container.style.minHeight = targetHeight + 'px';
        container.style.maxHeight = targetHeight + 'px';
        container.style.overflow = 'hidden';
    }
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间
 * @returns {Function} 防抖后的函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 搜索视频
 * @param {string} keyword - 搜索关键词
 */
function searchVideos(keyword) {
    // 跳转到视频列表页面进行搜索
    window.location.href = `/videos?keyword=${encodeURIComponent(keyword.trim())}`;
}

/**
 * 加载更多视频
 * @param {number} page - 页码
 */
async function loadMoreVideos(page) {
    try {
        const response = await fetch(`/api/videos?page=${page}&size=12`);
        const result = await response.json();
        
        if (result.success && result.data.length > 0) {
            appendVideosToGrid(result.data);
            updateLoadMoreButton(result.hasNext, page + 1);
        } else {
            showAlert('没有更多视频了', 'info');
        }
        
    } catch (error) {
        console.error('加载更多视频失败:', error);
        showAlert('加载失败，请稍后重试', 'danger');
    }
}

/**
 * 将视频添加到网格中
 * @param {Array} videos - 视频列表
 */
function appendVideosToGrid(videos) {
    const videoGrid = document.querySelector('.video-grid');
    if (!videoGrid) return;
    
    videos.forEach(function(video) {
        const videoCard = createVideoCard(video);
        videoGrid.appendChild(videoCard);
    });
    
    // 重新初始化新添加的卡片
    initializeVideoCards();
}

/**
 * 创建视频卡片元素
 * @param {Object} video - 视频对象
 * @returns {HTMLElement} 视频卡片元素
 */
function createVideoCard(video) {
    const article = document.createElement('article');
    article.className = 'video-card card h-100 shadow-sm';

    // 根据屏幕宽度设置缩略图高度
    const screenWidth = window.innerWidth;
    let thumbnailHeight;

    if (screenWidth <= 576) {
        thumbnailHeight = 220;
    } else if (screenWidth <= 768) {
        thumbnailHeight = 240;
    } else {
        thumbnailHeight = 260;
    }

    article.innerHTML = `
        <div class="video-thumbnail position-relative">
            <img src="${video.thumbnailUrl || '/images/default-thumbnail.jpg'}"
                 class="card-img-top thumbnail-optimized"
                 alt="${video.title}"
                 loading="lazy"
                 data-loaded="false"
                 style="width: 100%; height: ${thumbnailHeight}px; object-fit: cover; min-height: ${thumbnailHeight}px; max-height: ${thumbnailHeight}px;">
            <div class="play-overlay">
                <a href="/play/${video.id}" class="play-btn">
                    <i class="fas fa-play fa-2x"></i>
                </a>
            </div>
        </div>
        <div class="card-body">
            <h5 class="card-title">${video.title}</h5>
            <div class="video-stats">
                <div class="mb-1">
                    <small class="text-muted">
                        <i class="fas fa-calendar me-1"></i>
                        <span>${formatDate(video.createdTime)}</span>
                    </small>
                </div>
            </div>
        </div>
    `;

    // 为新创建的图片添加事件监听器
    const img = article.querySelector('.thumbnail-optimized');
    if (img) {
        img.addEventListener('load', function() {
            this.classList.add('loaded');
            this.setAttribute('data-loaded', 'true');
            forceThumbnailSize(this);
        });

        img.addEventListener('error', function() {
            this.src = '/images/default-thumbnail.jpg';
            this.alt = '默认缩略图';
            forceThumbnailSize(this);
        });

        // 立即设置尺寸
        forceThumbnailSize(img);
    }

    return article;
}

/**
 * 更新加载更多按钮
 * @param {boolean} hasNext - 是否有下一页
 * @param {number} nextPage - 下一页页码
 */
function updateLoadMoreButton(hasNext, nextPage) {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (!loadMoreBtn) return;
    
    if (hasNext) {
        loadMoreBtn.style.display = 'block';
        loadMoreBtn.onclick = () => loadMoreVideos(nextPage);
    } else {
        loadMoreBtn.style.display = 'none';
    }
}

/**
 * 格式化日期
 * @param {string} dateString - 日期字符串
 * @returns {string} 格式化后的日期
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    });
}



/**
 * 显示提示信息
 * @param {string} message - 提示消息
 * @param {string} type - 提示类型
 */
function showAlert(message, type = 'info') {
    // 使用全局的showAlert函数，如果存在的话
    if (window.VideoPlayer && window.VideoPlayer.showAlert) {
        window.VideoPlayer.showAlert(message, type);
        return;
    }
    
    // 降级处理
    const alertContainer = document.createElement('div');
    alertContainer.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertContainer.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    
    alertContainer.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertContainer);
    
    // 自动关闭
    setTimeout(function() {
        if (alertContainer.parentNode) {
            alertContainer.remove();
        }
    }, 3000);
}

/**
 * 筛选视频
 * @param {string} filterType - 筛选类型
 * @param {string} filterValue - 筛选值
 */
function filterVideos(filterType, filterValue) {
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.set(filterType, filterValue);
    currentUrl.searchParams.set('page', '0'); // 重置到第一页
    
    window.location.href = currentUrl.toString();
}

/**
 * 清除所有筛选
 */
function clearFilters() {
    const currentUrl = new URL(window.location);
    currentUrl.search = ''; // 清除所有查询参数
    
    window.location.href = currentUrl.toString();
}

// 导出到全局作用域
window.VideosPage = {
    searchVideos,
    loadMoreVideos,
    filterVideos,
    clearFilters,
    formatDate,
    showAlert,
    toggleSearch,
    initializeSearchToggle,
    initializeThumbnails,
    forceThumbnailSize,
    debounce
};

// 导出toggleSearch函数供HTML调用
window.toggleSearch = toggleSearch;
