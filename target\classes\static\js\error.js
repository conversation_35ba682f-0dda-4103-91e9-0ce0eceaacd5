/**
 * 错误页面专用JavaScript功能
 * Error Page JavaScript Functions
 * @version 1.0.0
 */

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeErrorPage();
});

/**
 * 初始化错误页面
 */
function initializeErrorPage() {
    console.log('🚨 错误页面初始化...');
    
    // 添加页面动画效果
    addPageAnimations();
    
    // 初始化按钮功能
    initializeButtons();
    
    // 添加自动重试功能
    initializeAutoRetry();
    
    // 记录错误信息
    logErrorInfo();
    
    console.log('✅ 错误页面初始化完成');
}

/**
 * 添加页面动画效果
 */
function addPageAnimations() {
    // 为错误容器添加淡入动画
    const errorContainer = document.querySelector('.error-container');
    if (errorContainer) {
        errorContainer.classList.add('fade-in');
    }
    
    // 为解决方案列表项添加延迟动画
    const listItems = document.querySelectorAll('.list-unstyled li');
    listItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateX(-20px)';
        item.style.transition = 'all 0.5s ease';
        
        setTimeout(() => {
            item.style.opacity = '1';
            item.style.transform = 'translateX(0)';
        }, 500 + index * 100);
    });
}

/**
 * 初始化按钮功能
 */
function initializeButtons() {
    // 返回上页按钮
    const backButton = document.querySelector('button[onclick="history.back()"]');
    if (backButton) {
        // 移除内联onclick，添加事件监听器
        backButton.removeAttribute('onclick');
        backButton.addEventListener('click', goBack);
        
        // 添加键盘支持
        backButton.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                goBack();
            }
        });
    }
    
    // 返回首页按钮增强
    const homeButton = document.querySelector('a[href="/"]');
    if (homeButton) {
        homeButton.addEventListener('click', function(e) {
            // 添加加载动画
            const icon = this.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-spinner fa-spin me-2';
            }
            
            // 延迟跳转以显示动画
            setTimeout(() => {
                if (icon) {
                    icon.className = 'fas fa-home me-2';
                }
            }, 1000);
        });
    }
    
    // 为所有按钮添加点击效果
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

/**
 * 返回上一页
 */
function goBack() {
    try {
        // 检查是否有历史记录
        if (window.history.length > 1) {
            window.history.back();
        } else {
            // 如果没有历史记录，跳转到首页
            window.location.href = '/';
        }
    } catch (error) {
        console.error('返回上页失败:', error);
        // 降级方案：跳转到首页
        window.location.href = '/';
    }
}

/**
 * 初始化自动重试功能
 */
function initializeAutoRetry() {
    // 添加重试按钮
    const buttonContainer = document.querySelector('.mt-4');
    if (buttonContainer) {
        const retryButton = document.createElement('button');
        retryButton.className = 'btn btn-success btn-lg ms-2';
        retryButton.innerHTML = '<i class="fas fa-redo me-2"></i>重试';
        retryButton.addEventListener('click', retryCurrentPage);
        
        buttonContainer.appendChild(retryButton);
    }
    
    // 添加自动重试倒计时（可选）
    if (shouldAutoRetry()) {
        startAutoRetryCountdown();
    }
}

/**
 * 重试当前页面
 */
function retryCurrentPage() {
    const button = event.target.closest('.btn');
    const icon = button.querySelector('i');
    
    // 显示加载状态
    icon.className = 'fas fa-spinner fa-spin me-2';
    button.disabled = true;
    button.innerHTML = '<span class="loading-animation"></span> 重试中...';
    
    // 延迟重新加载页面
    setTimeout(() => {
        window.location.reload();
    }, 1000);
}

/**
 * 判断是否应该自动重试
 */
function shouldAutoRetry() {
    // 检查错误类型，某些错误可以自动重试
    const errorAlert = document.querySelector('.alert-danger');
    if (errorAlert) {
        const errorText = errorAlert.textContent.toLowerCase();
        return errorText.includes('网络') || 
               errorText.includes('连接') || 
               errorText.includes('超时') ||
               errorText.includes('加载');
    }
    return false;
}

/**
 * 开始自动重试倒计时
 */
function startAutoRetryCountdown() {
    let countdown = 10;
    const countdownElement = document.createElement('div');
    countdownElement.className = 'alert alert-info mt-3';
    countdownElement.innerHTML = `
        <i class="fas fa-clock me-2"></i>
        页面将在 <span id="countdown">${countdown}</span> 秒后自动重试
        <button type="button" class="btn btn-sm btn-outline-info ms-2" onclick="cancelAutoRetry()">取消</button>
    `;
    
    const errorContainer = document.querySelector('.error-container');
    if (errorContainer) {
        errorContainer.appendChild(countdownElement);
    }
    
    const countdownSpan = document.getElementById('countdown');
    const interval = setInterval(() => {
        countdown--;
        if (countdownSpan) {
            countdownSpan.textContent = countdown;
        }
        
        if (countdown <= 0) {
            clearInterval(interval);
            window.location.reload();
        }
    }, 1000);
    
    // 保存interval ID以便取消
    window.autoRetryInterval = interval;
}

/**
 * 取消自动重试
 */
function cancelAutoRetry() {
    if (window.autoRetryInterval) {
        clearInterval(window.autoRetryInterval);
        window.autoRetryInterval = null;
    }
    
    const countdownAlert = document.querySelector('.alert-info');
    if (countdownAlert) {
        countdownAlert.remove();
    }
}

/**
 * 记录错误信息
 */
function logErrorInfo() {
    const errorAlert = document.querySelector('.alert-danger');
    const warningAlert = document.querySelector('.alert-warning');
    
    let errorMessage = '未知错误';
    if (errorAlert) {
        errorMessage = errorAlert.textContent.trim();
    } else if (warningAlert) {
        errorMessage = warningAlert.textContent.trim();
    }
    
    // 记录到控制台
    console.error('🚨 页面错误:', {
        message: errorMessage,
        url: window.location.href,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent
    });
    
    // 可选：发送错误报告到服务器
    if (window.location.hostname !== 'localhost') {
        sendErrorReport(errorMessage);
    }
}

/**
 * 发送错误报告到服务器
 */
function sendErrorReport(errorMessage) {
    try {
        fetch('/api/error-report', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: errorMessage,
                url: window.location.href,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent
            })
        }).catch(err => {
            console.warn('发送错误报告失败:', err);
        });
    } catch (error) {
        console.warn('发送错误报告异常:', error);
    }
}

/**
 * 添加键盘快捷键支持
 */
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // ESC键返回上页
        if (e.key === 'Escape') {
            goBack();
        }
        
        // F5或Ctrl+R刷新页面
        if (e.key === 'F5' || (e.ctrlKey && e.key === 'r')) {
            e.preventDefault();
            retryCurrentPage();
        }
        
        // Alt+H返回首页
        if (e.altKey && e.key === 'h') {
            e.preventDefault();
            window.location.href = '/';
        }
    });
}

/**
 * 显示帮助信息
 */
function showHelpInfo() {
    const helpModal = document.createElement('div');
    helpModal.className = 'modal fade';
    helpModal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-question-circle me-2"></i>帮助信息
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <h6>键盘快捷键：</h6>
                    <ul>
                        <li><kbd>ESC</kbd> - 返回上一页</li>
                        <li><kbd>F5</kbd> 或 <kbd>Ctrl+R</kbd> - 重试</li>
                        <li><kbd>Alt+H</kbd> - 返回首页</li>
                    </ul>
                    <h6 class="mt-3">常见问题：</h6>
                    <ul>
                        <li>如果是网络问题，请检查网络连接</li>
                        <li>如果是视频问题，请联系管理员</li>
                        <li>如果问题持续存在，请清除浏览器缓存</li>
                    </ul>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(helpModal);
    const modal = new bootstrap.Modal(helpModal);
    modal.show();
    
    // 模态框关闭后移除元素
    helpModal.addEventListener('hidden.bs.modal', function() {
        helpModal.remove();
    });
}

// 初始化键盘快捷键
initializeKeyboardShortcuts();

// 导出函数供全局使用
window.goBack = goBack;
window.retryCurrentPage = retryCurrentPage;
window.cancelAutoRetry = cancelAutoRetry;
window.showHelpInfo = showHelpInfo;

// 控制台输出错误页面信息
console.log(`
🚨 佳茵轻康 - 错误页面
📅 版本: 1.0.0
🚀 状态: 已就绪
💡 提示: 使用ESC键返回上页，F5重试
`);
