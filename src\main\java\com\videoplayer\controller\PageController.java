package com.videoplayer.controller;

import com.videoplayer.entity.Video;
import com.videoplayer.service.VideoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;

/**
 * 页面控制器
 * 处理前端页面路由和数据传递
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Controller
public class PageController {

    @Autowired
    private VideoService videoService;
    
    /**
     * 处理视频URL（已简化，直接使用原URL）
     */
    private void processVideoUrl(Video video) {
        // 简化版本：直接使用数据库中的URL，不做额外处理
        // 如果需要URL处理逻辑，可以在这里添加
    }

    /**
     * 首页 - 显示视频列表
     */
    @GetMapping("/")
    public String index(Model model) {
        try {
            List<Video> popularVideos = videoService.getPopularVideos(12);
            
            model.addAttribute("popularVideos", popularVideos);
            model.addAttribute("pageTitle", "佳茵轻康 - 首页");
            
            return "index";
        } catch (Exception e) {
            model.addAttribute("error", "加载视频列表失败: " + e.getMessage());
            return "error";
        }
    }

    /**
     * 所有视频页面
     */
    @GetMapping("/videos")
    public String videos(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "12") int size,
            @RequestParam(required = false) String keyword,
            HttpServletRequest request,
            Model model) {
        try {
            // 根据设备类型调整每页显示数量
            int actualSize = getPageSizeForDevice(request, size);

            List<Video> videos;
            boolean hasNextPage = false;
            long totalVideos = 0;
            int totalPages = 0;

            if (keyword != null && !keyword.trim().isEmpty()) {
                videos = videoService.searchVideosList(keyword, page, actualSize);
                model.addAttribute("keyword", keyword);

                // 计算搜索结果的总数和总页数
                var searchPage = videoService.searchVideos(keyword, 0, 1);
                totalVideos = searchPage.getTotalElements();
                totalPages = (int) Math.ceil((double) totalVideos / actualSize);

                // 如果当前页没有视频且不是第一页，重定向到第一页
                if (videos.isEmpty() && page > 0) {
                    return "redirect:/videos?keyword=" + keyword;
                }

                // 检查是否有下一页：尝试获取下一页的第一个视频
                List<Video> nextPageVideos = videoService.searchVideosList(keyword, page + 1, 1);
                hasNextPage = !nextPageVideos.isEmpty();
            } else {
                videos = videoService.getAllVideos(page, actualSize);

                // 获取总视频数和计算总页数
                totalVideos = videoService.getTotalVideoCount();
                totalPages = (int) Math.ceil((double) totalVideos / actualSize);

                // 如果当前页没有视频且不是第一页，重定向到第一页
                if (videos.isEmpty() && page > 0) {
                    return "redirect:/videos";
                }

                // 检查是否有下一页：尝试获取下一页的第一个视频
                List<Video> nextPageVideos = videoService.getAllVideos(page + 1, 1);
                hasNextPage = !nextPageVideos.isEmpty();
            }

            // 处理视频URL
            videos.forEach(this::processVideoUrl);

            model.addAttribute("videos", videos);
            model.addAttribute("currentPage", page);
            model.addAttribute("pageSize", actualSize);
            model.addAttribute("hasNextPage", hasNextPage);
            model.addAttribute("totalPages", totalPages);
            model.addAttribute("pageTitle", "佳茵轻康 - 视频库");
            model.addAttribute("isMobile", isMobileDevice(request));

            return "videos";
        } catch (Exception e) {
            model.addAttribute("error", "加载视频列表失败: " + e.getMessage());
            return "error";
        }
    }

    /**
     * 视频播放页面
     */
    @GetMapping("/play/{id}")
    public String playVideo(@PathVariable String id, Model model) {
        try {
            Optional<Video> videoOptional = videoService.getVideoById(id);
            
            if (videoOptional.isPresent()) {
                Video video = videoOptional.get();
                
                // 处理视频URL
                processVideoUrl(video);
                
                model.addAttribute("video", video);
                model.addAttribute("pageTitle", video.getTitle());
                
                return "play";
            } else {
                model.addAttribute("error", "视频不存在或已被删除");
                return "error";
            }
        } catch (Exception e) {
            model.addAttribute("error", "加载视频失败: " + e.getMessage());
            return "error";
        }
    }




    /**
     * 管理页面 - 隐藏路径
     */
    @GetMapping("/jyqk-admin")
    public String adminPanel(
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "4") int size,
            Model model) {
        try {
            List<Video> allVideos;

            if (search != null && !search.trim().isEmpty()) {
                // 搜索视频
                allVideos = videoService.searchVideosByTitle(search);
                model.addAttribute("search", search);
            } else {
                // 获取所有视频（包括禁用的）
                allVideos = videoService.getAllVideosForAdmin();
            }

            // 根据状态筛选
            if (status != null && !status.isEmpty()) {
                if ("active".equals(status)) {
                    allVideos = allVideos.stream().filter(Video::getIsActive).collect(java.util.stream.Collectors.toList());
                } else if ("inactive".equals(status)) {
                    allVideos = allVideos.stream().filter(v -> !v.getIsActive()).collect(java.util.stream.Collectors.toList());
                }
                model.addAttribute("status", status);
            }

            // 分页处理
            int totalVideos = allVideos.size();
            int totalPages = (int) Math.ceil((double) totalVideos / size);
            int startIndex = page * size;
            int endIndex = Math.min(startIndex + size, totalVideos);

            List<Video> videos = allVideos.subList(startIndex, endIndex);

            // 处理视频URL
            videos.forEach(this::processVideoUrl);

            model.addAttribute("videos", videos);
            model.addAttribute("currentPage", page);
            model.addAttribute("pageSize", size);
            model.addAttribute("totalPages", totalPages);
            model.addAttribute("totalVideos", totalVideos);
            model.addAttribute("hasNextPage", page < totalPages - 1);
            model.addAttribute("hasPrevPage", page > 0);
            model.addAttribute("pageTitle", "佳茵轻康 - 视频管理");

            return "admin";
        } catch (Exception e) {
            model.addAttribute("error", "加载管理页面失败: " + e.getMessage());
            return "error";
        }
    }

    /**
     * 添加视频页面 - 隐藏路径
     */
    @GetMapping("/jyqk-admin/add")
    public String addVideoPage(Model model) {
        model.addAttribute("video", new Video());
        model.addAttribute("pageTitle", "佳茵轻康 - 添加视频");
        return "add-video";
    }

    /**
     * 编辑视频页面 - 隐藏路径
     */
    @GetMapping("/jyqk-admin/edit/{id}")
    public String editVideoPage(@PathVariable String id, Model model) {
        try {
            Optional<Video> videoOptional = videoService.getVideoById(id);
            
            if (videoOptional.isPresent()) {
                model.addAttribute("video", videoOptional.get());
                model.addAttribute("pageTitle", "佳茵轻康 - 编辑视频");
                return "edit-video";
            } else {
                model.addAttribute("error", "视频不存在");
                return "error";
            }
        } catch (Exception e) {
            model.addAttribute("error", "加载编辑页面失败: " + e.getMessage());
            return "error";
        }
    }

    /**
     * 关于页面
     */
    @GetMapping("/about")
    public String about(Model model) {
        model.addAttribute("pageTitle", "佳茵轻康 - 关于我们");
        return "about";
    }



    /**
     * 检测是否为移动设备
     */
    private boolean isMobileDevice(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        if (userAgent == null) {
            return false;
        }

        userAgent = userAgent.toLowerCase();
        return userAgent.contains("mobile") ||
               userAgent.contains("android") ||
               userAgent.contains("iphone") ||
               userAgent.contains("ipad") ||
               userAgent.contains("ipod") ||
               userAgent.contains("blackberry") ||
               userAgent.contains("windows phone");
    }

    /**
     * 根据设备类型获取每页显示数量
     */
    private int getPageSizeForDevice(HttpServletRequest request, int defaultSize) {
        if (isMobileDevice(request)) {
            // 手机端每页显示2个视频
            return 2;
        }
        // 桌面端使用默认值
        return defaultSize;
    }
}

