/**
 * 关于页面专用JavaScript
 * About Page JavaScript Functions
 * @version 1.0.0
 */

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeAboutPage();
});

/**
 * 初始化关于页面
 */
function initializeAboutPage() {
    console.log('🚀 关于页面初始化...');
    
    // 添加页面动画效果
    addPageAnimations();
    
    // 初始化联系卡片交互
    initializeContactCards();
    
    console.log('✅ 关于页面初始化完成');
}

/**
 * 添加页面动画效果
 */
function addPageAnimations() {
    // 为卡片添加淡入动画
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        
        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

/**
 * 初始化联系卡片交互
 */
function initializeContactCards() {
    const contactCards = document.querySelectorAll('.card');
    
    contactCards.forEach(card => {
        // 添加悬停效果
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
        
        // 添加点击复制功能（如果卡片包含电话号码）
        const phoneNumbers = card.querySelectorAll('strong');
        phoneNumbers.forEach(phoneElement => {
            const text = phoneElement.textContent;
            if (text.includes('微信/电话：')) {
                const phone = text.replace('微信/电话：', '');
                phoneElement.style.cursor = 'pointer';
                phoneElement.title = '点击复制电话号码';
                
                phoneElement.addEventListener('click', function(e) {
                    e.stopPropagation();
                    copyToClipboard(phone);
                });
            }
        });
    });
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 */
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showNotification('电话号码已复制到剪贴板: ' + text, 'success');
        }).catch(err => {
            console.error('复制失败:', err);
            showNotification('复制失败，请手动复制', 'error');
        });
    } else {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showNotification('电话号码已复制到剪贴板: ' + text, 'success');
        } catch (err) {
            console.error('复制失败:', err);
            showNotification('复制失败，请手动复制', 'error');
        }
        document.body.removeChild(textArea);
    }
}

/**
 * 显示通知消息
 * @param {string} message - 通知消息
 * @param {string} type - 通知类型 (success, error, info)
 */
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

/**
 * 平滑滚动到页面顶部
 */
function scrollToTop() {
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
}

/**
 * 检查元素是否在视口中
 * @param {Element} element - 要检查的元素
 * @returns {boolean} - 是否在视口中
 */
function isElementInViewport(element) {
    const rect = element.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

// 导出函数供全局使用
window.copyToClipboard = copyToClipboard;
window.showNotification = showNotification;
window.scrollToTop = scrollToTop;
window.isElementInViewport = isElementInViewport;

// 控制台输出欢迎信息
console.log(`
📞 佳茵轻康 - 关于我们
📅 版本: 1.0.0
🚀 状态: 已就绪
💡 提示: 点击电话号码可以复制到剪贴板
`);
