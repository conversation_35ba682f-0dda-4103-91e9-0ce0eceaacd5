/**
 * 添加视频页面专用样式 - 强制防夜间模式
 * Add Video Page Styles - Force Light Mode
 */

/* 添加视频页面防夜间模式设置 - 包含内联样式 */
:root {
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

html, body {
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
    -webkit-color-scheme: light only !important;
}

/* 从HTML内联样式移动过来的防夜间模式设置 */
/* 这些样式确保页面始终保持浅色主题 */

/* Body样式 - 从内联样式移动 */
body {
    background-color: #ffffff !important;
    color: #333333 !important;
    color-scheme: light only !important;
}

/* 导航栏样式 - 从内联样式移动 */
.navbar.navbar-dark.bg-primary.sticky-top {
    background-color: #0d6efd !important;
    color-scheme: light only !important;
}

/* 表单样式增强 */
.form-control:focus,
.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 视频预览容器 */
#videoPreview {
    min-height: 50px;
}

#videoPreview video {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
}

/* 缩略图预览容器 */
#thumbnailPreview {
    min-height: 50px;
}

#thumbnailPreview img {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background-color: #f8f9fa;
}

/* 上传进度样式 - 包含内联样式 */
#uploadProgress {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e9ecef;
    display: none; /* 从内联样式移动 */
}

#uploadProgress .progress {
    height: 8px;
    border-radius: 4px;
}

/* 全局提示容器样式 - 从内联样式移动 */
#alertContainer {
    z-index: 9999;
    margin-top: 20px;
}

/* 进度条初始宽度 - 从内联样式移动 */
.progress-bar {
    width: 0%;
}

/* 加载遮罩样式 - 包含内联样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none; /* 从内联样式移动 */
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

#loadingOverlay {
    display: none; /* 从内联样式移动 */
}

.loading-content {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    color: #333;
}

/* 表单卡片样式 */
.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.card-body {
    padding: 2rem;
}

/* 按钮样式增强 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 表单标签样式 */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-label i {
    color: #6c757d;
}

/* 必填字段标识 */
.text-danger {
    font-weight: 700;
}

/* 输入框样式 */
.form-control,
.form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 0.75rem;
    transition: all 0.2s ease;
}

.form-control:hover,
.form-select:hover {
    border-color: #adb5bd;
}

/* 文本域样式 */
textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

/* 预览区域样式 */
.preview-container {
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    transition: all 0.2s ease;
}

.preview-container:hover {
    border-color: #adb5bd;
    background-color: #e9ecef;
}

/* 警告和错误样式 */
.alert {
    border-radius: 8px;
    border: none;
    font-weight: 500;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.alert-success {
    background-color: #d1edff;
    color: #0c5460;
}

/* 页面标题样式 */
.h3 {
    color: #495057;
    font-weight: 700;
}

/* 返回按钮样式 */
.btn-outline-secondary {
    border-color: #6c757d;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem;
    }
    
    .col-md-8 {
        padding: 0 1rem;
    }
    
    .btn {
        font-size: 0.9rem;
        padding: 0.6rem 1rem;
    }
    
    .h3 {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .card-body {
        padding: 1rem;
    }
    
    .form-control,
    .form-select {
        font-size: 0.9rem;
    }
    
    .btn {
        font-size: 0.85rem;
        padding: 0.5rem 0.8rem;
    }
    
    .d-grid.gap-2.d-md-flex {
        gap: 0.5rem !important;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* 输入验证样式 */
.is-invalid {
    border-color: #dc3545;
}

.is-valid {
    border-color: #198754;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #198754;
}

/* ========== 添加视频页面最终防夜间模式保护 ========== */
@media screen {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}

@media screen and (max-device-width: 768px) {
    html, body, * {
        color-scheme: light only !important;
        -webkit-color-scheme: light only !important;
    }
}
